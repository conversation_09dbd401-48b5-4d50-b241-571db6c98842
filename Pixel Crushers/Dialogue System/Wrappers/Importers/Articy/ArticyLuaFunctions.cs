#if USE_ARTICY
// Copyright (c) Pixel Crushers. All rights reserved.

using UnityEngine;

namespace PixelCrushers.DialogueSystem.Articy.Wrappers
{

    /// <summary>
    /// This wrapper class keeps references intact if you switch between the 
    /// compiled assembly and source code versions of the original class.
    /// </summary>
    [AddComponentMenu("Pixel Crushers/Dialogue System/Third Party/articy:draft/Articy Lua Functions")]
    public class ArticyLuaFunctions : PixelCrushers.DialogueSystem.Articy.ArticyLuaFunctions
    {
    }

}
#endif
