// Copyright Isto Inc.

using Isto.Core.UI;
using UnityEngine;

namespace Isto.Core.Cheats
{
    [CreateAssetMenu(fileName = "New List All Commands Action", menuName = "Scriptables/Dev Mode Actions/List All Commands Action")]
    public class ListAllCommandsDeveloperAction : CheatAction
    {
        public override void Activate(params object[] args)
        {
            var console = GameObject.FindObjectOfType<UIConsole>();
            console.ShowAllAvailableCommands();
        }

        public override void Deactivate()
        {
            // Not used
        }

        public override void OnUpdate()
        {
            // Not used
        }
    }
}