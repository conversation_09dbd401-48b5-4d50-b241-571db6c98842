// Copyright Isto Inc.
using UnityEngine;
using Zenject;

#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
#endif

using Isto.Core.Tiles;

namespace Isto.Core
{
    public class TilemapInstaller : MonoInstaller<TilemapInstaller>, IActionOnSceneOpenInEditor
    {
        public TilemapCullingSettings tilemapSettings;

        public override void InstallBindings()
        {
            // Register this scenes tilemap settings with the global controller
            TilemapGlobalController globalTilemaps = Container.Resolve<TilemapGlobalController>();

            if (globalTilemaps != null)
                globalTilemaps.AddTilemapSettings(tilemapSettings);

            // Bind the settings for this scene for use by scripts in this scene
            Container.Bind<TilemapCullingSettings>().FromInstance(tilemapSettings).AsSingle();
        }


#if UNITY_EDITOR
        public UnityEditor.SceneAsset tilemapScene;

        [ContextMenu("Load Sliced Prefabs")]
        public void LoadSlicedPrefabsForTilemap()
        {
            var settings = TilemapUtils.GetGroundMap(gameObject.scene.name);

            if (settings != null)
            {
                for (int i = 0; i < settings.tilemapPrefabs.Count; i++)
                {
                    TilemapPrefabSetup setup = settings.tilemapPrefabs[i];

                    Instantiate(setup.prefab, transform);
                }
            }
        }
#endif

        public void DoAction()
        {
#if UNITY_EDITOR
            if (tilemapScene == null && tilemapSettings.sliceSize != 0)
            {
                Debug.LogWarning($"No Tilemap scene set for this {gameObject.scene.name}.  If using the TilemapCullingController with a secondardy scene holding the tilemaps, make sure this is set");
                return;
            }
            else if (tilemapScene == null)
            {
                return;
            }

            bool tilemapSceneLoaded = false;

            TilemapCullingController tilemapCulling = FindObjectOfType<TilemapCullingController>();

            bool cullingControllerExistsAndActive = tilemapCulling != null;

            if (cullingControllerExistsAndActive)
            {
                Debug.Log($"TilemapCullingController is active so Tilemap specific scene {tilemapScene.name} will not be loaded and tilemap culling will be used instead." +
                    $"  If this is not desired disable the TilemapCullingController Object {tilemapCulling.gameObject.name}");
            }

            for (int i = 0; i < EditorSceneManager.sceneCount; i++)
            {
                string sceneName = EditorSceneManager.GetSceneAt(i).name;

                if (sceneName == tilemapScene.name)
                    tilemapSceneLoaded = true;
            }

            if (!tilemapSceneLoaded && !cullingControllerExistsAndActive)
            {
                string scenePath = AssetDatabase.GetAssetPath(tilemapScene);
                EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Additive);
            }
#endif
        }
    }
}