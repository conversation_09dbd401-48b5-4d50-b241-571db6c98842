// Copyright Isto Inc.
using Isto.Core.Data;
using Isto.Core.Items;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.Core.Automation
{
    [Serializable]
    public class AutomationGridSpace
    {
        public Vector3 position;

        public IItemProcessor itemProcessor;
        public AutomationResource Resource { get; private set; }
        public AutomationCoreItem ActiveItem { get { return _itemsOnSpace?.Count > 0 ? _itemsOnSpace[0] : null; } }
        public GameObject DisplayItem { get; set; }
        public bool Powered { get; private set; }
        public List<AutomationCoreItem> AllItemsOnSpace { get { return _itemsOnSpace; } }
        public bool Sleeping { get; private set; }

        // Tells the culling system that this tile is already being drawn
        // Otherwise we might get doubles when drawing things that already were drawn because of their initial spawning
        // or because of redundant instructions in the culling system
        // Split the previous IsRendered flag into separate processor and resource ones, in case a resource is created during load before the processor is actually visible, blocking the display of the processor
        // Happens on Planter because both exist on the space at the same time
        public bool ResourceRendered = false;
        public bool ProcessorRendered = false;

        // Tracks items that are on grid space but not currently availble to be used by automation (Items dropped by player on grid, etc)
        private List<AutomationCoreItem> _itemsOnSpace = new List<AutomationCoreItem>();

        private List<IItemMoveType> _runningItemMoves = new List<IItemMoveType>();

        public AutomationGridSpace(Vector3 position)
        {
            this.position = new Vector3(Mathf.RoundToInt(position.x), 0f, Mathf.RoundToInt(position.z));
        }

        /// <summary>
        /// Full parameter constructor used when loading from data and creating space with all possible items set
        /// </summary>
        /// <param name="position"></param>
        /// <param name="processor"></param>
        /// <param name="resouce"></param>
        /// <param name="items"></param>
        public AutomationGridSpace(Vector3 position, IItemProcessor processor, AutomationResource resouce, List<AutomationCoreItem> items)
        {
            this.position = position;
            itemProcessor = processor;

            if (processor is IAutomationGridSpaceUser)
            {
                IAutomationGridSpaceUser gridUser = processor as IAutomationGridSpaceUser;
                gridUser.SetGridSpace(this);
            }

            Resource = resouce;

            if (items != null && items.Count > 0)
            {
                _itemsOnSpace = items;
            }
        }

        public void Tick(float deltaTime)
        {
            TickMovers(deltaTime);

            if (!itemProcessor.IsNullOrDestroyed())
                itemProcessor.Tick(deltaTime);

            if (!Resource.IsNullOrDestroyed())
            {
                if (Resource.Health == 0)
                    Resource = null;
                else
                    Resource.Tick(deltaTime);
            }
        }

        private void TickMovers(float deltaTime)
        {
            int moversCount = _runningItemMoves.Count;

            for (int i = moversCount - 1; i >= 0; i--)
            {
                _runningItemMoves[i].Tick(deltaTime);

                // The Tick can cause some new moves to override/cancel other moves, so we can actually overflow here
                if (i < _runningItemMoves.Count && _runningItemMoves[i].Complete)
                    _runningItemMoves.RemoveAt(i);
            }
        }

        public void SetPowered(bool isPowered)
        {
            Powered = isPowered;
        }

        public bool IsEmpty()
        {
            return itemProcessor.IsNullOrDestroyed() && Resource == null && ActiveItem == null;
        }

        public bool IsItemOnSpace(string itemID)
        {
            if (ActiveItem == null)
                return false;

            if (ActiveItem.ID.Equals(itemID, StringComparison.CurrentCultureIgnoreCase))
                return true;

            for (int i = 0; i < _itemsOnSpace.Count; i++)
            {
                if (_itemsOnSpace[i].ID.Equals(itemID, StringComparison.CurrentCultureIgnoreCase))
                    return true;
            }

            return false;
        }

        public int GetCountOfItemsIncoming(string itemId)
        {
            int itemsIncoming = 0;

            for (int i = 0; i < AllItemsOnSpace.Count; i++)
            {
                if (AllItemsOnSpace[i].ID.Equals(itemId, StringComparison.OrdinalIgnoreCase))
                    itemsIncoming += AllItemsOnSpace[i].count;
            }

            return itemsIncoming;
        }

        public bool ContainsItem(AutomationCoreItem item)
        {
            return _itemsOnSpace.Contains(item);
        }

        public bool ContainsItem(string itemID)
        {
            for (int i = 0; i < _itemsOnSpace.Count; i++)
            {
                if (_itemsOnSpace[i].ID.Equals(itemID))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Raises item consumed event and removes the item from the space.
        /// If you want to remove an AutomationCoreItem from the system, use AutomationSystem.DeleteCoreItem() which will in turn
        /// call this method and also deal with extra cleanup and events.
        /// </summary>
        public void ConsumeItem(AutomationCoreItem item)
        {
            if (_itemsOnSpace.Contains(item))
            {
                _itemsOnSpace.Remove(item);
                item.ConsumeItem();
            }
            else
            {
                Debug.LogError($"Can't consume item {item.ID} because it does not exist on this space: {position}");
            }
        }

        /// <summary>
        /// Sets the CoreItem to null, without sending events. Usually used when item moved to another space
        /// </summary>
        public void ClearItem(AutomationCoreItem item)
        {
            if (ActiveItem == item)
                _itemsOnSpace.RemoveAt(0);
            else
                _itemsOnSpace.Remove(item);
        }

        public void ClearAllItems()
        {
            _itemsOnSpace.Clear();
        }

        public bool TrySetItem(AutomationCoreItem coreItem, bool ignoreProcessor = false)
        {
            bool processorAccepts = !itemProcessor.IsNullOrDestroyed() && itemProcessor.IsAvailable(coreItem) >= coreItem.count;

            if (itemProcessor.IsNullOrDestroyed() || processorAccepts || ignoreProcessor)
            {
                //Debug.Log($"Adding {coreItem.ID} to space at {position}");

                coreItem.SetGridSpace(this);
                _itemsOnSpace.Add(coreItem);

                return true;
            }

            return false;
        }

        public bool TryGetItem(out AutomationCoreItem item, string itemID)
        {
            for (int i = 0; i < _itemsOnSpace.Count; i++)
            {
                if (_itemsOnSpace[i].ID.Equals(itemID))
                {
                    item = _itemsOnSpace[i];
                    return true;
                }
            }

            item = null;
            return false;
        }

        public bool TrySetResource(AutomationResource resource, bool visualCulling)
        {
            if (Resource != null)
                return false;

            Resource = resource;
            Resource.VisualCulling = visualCulling;
            Resource.Space = this;

            return true;
        }

        public void ClearResource()
        {
            if (Resource != null)
                Resource.Remove();

            Resource = null;
        }

        public void RemoveProcessor()
        {
            if (itemProcessor is IAutomationActionOnRemoved actionOnRemove)
                actionOnRemove.Removed();

            itemProcessor = null;
        }

        public void MoveItemToSpace(IItemMoveType moveType)
        {
            _runningItemMoves.Add(moveType);
        }

        public void CancelAllMoves()
        {
            for (int i = 0; i < _runningItemMoves.Count; i++)
            {
                _runningItemMoves[i].Cancel();
            }

            _runningItemMoves.Clear();
        }

        public void CancelItemMove(AutomationCoreItem item)
        {
            int index = _runningItemMoves.IndexOf(item.MoveType);

            if (index != -1)
            {
                _runningItemMoves[index].Cancel();
                _runningItemMoves.RemoveAt(index);
            }
        }

        public void SetSleepingFlag()
        {
            Sleeping = true;
        }

        public void ClearSleepingFlag()
        {
            Sleeping = false;
        }

        public AutomationGridSpaceData GetSaveData()
        {
            if (IsEmpty())
                return null;

            AutomationGridSpaceData data = new AutomationGridSpaceData() { position = position };

            if (Resource != null)
            {
                data.resourceHealth = Resource.Health;
                data.resourceID = Resource.ID;
                data.growthPercent = Resource.GrowthPercent;

                if (Resource.ID.Equals("NO ID SET", StringComparison.CurrentCultureIgnoreCase))
                    Debug.LogWarning($"No id set for Resource on space at {position}");
            }

            foreach (AutomationCoreItem item in _itemsOnSpace)
            {
                data.items.Add(new ItemData(item.ID, "", item.count));
            }

            if (itemProcessor != null)
            {
                data.processorData = itemProcessor.GetSaveData();

                if (data.processorData != null)
                    data.processorData.position = position;
            }

            if (!data.IsEmpty())
                return data;
            else
                return null;
        }

        public int OrthogonalDistance(AutomationGridSpace target)
        {
            return OrthogonalXDistance(target) + OrthogonalZDistance(target);
        }

        public int OrthogonalXDistance(AutomationGridSpace target)
        {
            float xDist = Mathf.Abs(target.position.x - this.position.x);
            return Mathf.RoundToInt(xDist); // xDist should be pretty close to an integer already
        }

        public int OrthogonalZDistance(AutomationGridSpace target)
        {
            float zDist = Mathf.Abs(target.position.z - this.position.z);
            return Mathf.RoundToInt(zDist); // zDist should be pretty close to an integer already
        }

        /// <summary>
        /// If grid space has floating items around, grab one of them to be used next, otherwise returns null
        /// </summary>
        /// <returns>AutomationCoreItem if one is in floating queue, null otherwise</returns>
        private AutomationCoreItem GetNextFloatingItem()
        {
            if (_itemsOnSpace.Count > 0)
            {
                AutomationCoreItem firstItem = _itemsOnSpace[0];
                _itemsOnSpace.RemoveAt(0);

                return firstItem;
            }

            return null;
        }
    }
}