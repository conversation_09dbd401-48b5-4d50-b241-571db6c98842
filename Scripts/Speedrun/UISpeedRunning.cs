// Copyright Isto Inc.
using UnityEngine;
using Zenject;

namespace Isto.Core.Speedrun
{
    public class UISpeedRunning : MonoBehaviour
    {
        [SerializeField] private GameObject _speedrunTimerPrefab;

        private GameObject _speedRunTimerObject;
        private bool _initialized = false;
        private bool _previousSetting = false;

        // INJECTION

        private SpeedrunSettings _speedrunSettings; // optional

        [Inject]
        public void Inject([InjectOptional] SpeedrunSettings speedrunSettings)
        {
            _speedrunSettings = speedrunSettings;
        }


        // LIFECYCLE

        private void Start()
        {
            bool showSpeedRun = IsSpeedrunTimerUIActive();
            _previousSetting = showSpeedRun;
            if (showSpeedRun)
            {
                if (!_initialized)
                {
                    InstatiateSpeedRunTimer();
                }

                _speedRunTimerObject.SetActive(true);
            }
        }

        private void Update()
        {
            bool showSpeedRun = IsSpeedrunTimerUIActive();
            if (showSpeedRun != _previousSetting)
            {
                if (showSpeedRun)
                {
                    if (!_initialized)
                    {
                        InstatiateSpeedRunTimer();
                    }

                    _speedRunTimerObject.SetActive(true);
                }
                else
                {
                    _speedRunTimerObject.SetActive(false);
                }

                _previousSetting = showSpeedRun;
            }
        }


        // OTHER METHODS

        private bool IsSpeedrunTimerUIActive()
        {
            bool showSpeedRun = PlayerPrefs.GetInt(SpeedrunSettings.MENU_ACTIVE_PLAYER_PREFS_KEY, defaultValue: 0) == 1;

            if (_speedrunSettings == null || _speedrunSettings.CanUserToggleTimer())
            {
                // If the speedrun settings are not there, the project may be doing its own thing.
                // Like using a different system to control the timer.
                // Just rely on the setting value as if the user had set it.
                return showSpeedRun;
            }
            else if (_speedrunSettings.GetTimerConfigsCount() > 0)
            {
                // User can't toggle timer on/off, but there are some "styles" defined.
                // We use this is we want the timer to always be spawned, but with a "off" style option instead.
                // This gives more flexibility to the timer for what might be shown when it's off, e.g. a button that
                // turns it on.
                return true;
            }
            else
            {
                // If all else fails...
                return _speedrunSettings.GetDefaultTimerIsOnSetting();
            }
        }

        private void InstatiateSpeedRunTimer()
        {
            _speedRunTimerObject = Instantiate(_speedrunTimerPrefab, this.transform);
            _initialized = true;
        }
    }
}
