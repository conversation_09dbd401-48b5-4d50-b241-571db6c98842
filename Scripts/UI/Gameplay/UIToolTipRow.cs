// Copyright Isto Inc.
using Isto.Core.Localization;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Zenject;

namespace Isto.Core.UI
{
    public class UIToolTipRow : MonoBehaviour
    {
        // Public Variables

        public Image controlImg;
        public Image secondaryImg;
        public TextMeshProUGUI toolTipText;

        // Methods		

        public void SetRow(Sprite controlIcon, string toolTip, Sprite secondaryIcon = null)
        {
            controlImg.sprite = controlIcon;
            controlImg.gameObject.SetActive(controlIcon != null);

            if (secondaryIcon == null)
                secondaryImg.gameObject.SetActive(false);
            else
            {
                secondaryImg.gameObject.SetActive(true);
                secondaryImg.sprite = secondaryIcon;
            }

            Loc.SetTMProLocalized(toolTipText, toolTip?.ToUpper());
        }

        public class Pool : MonoMemoryPool<Sprite, string, Sprite, UIToolTipRow>
        {
            protected override void Reinitialize(Sprite icon, string toolTip, Sprite secondaryIcon, UIToolTipRow item)
            {
                item.SetRow(icon, toolTip, secondaryIcon);
            }
        }
    }
}