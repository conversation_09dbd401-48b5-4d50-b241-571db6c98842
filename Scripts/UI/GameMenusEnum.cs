// Copyright Isto Inc.

namespace Isto.Core.UI
{
    public class GameMenusEnum : MenusEnum
    {
        #region Deprecated
        // Kept around for Atrio support without having to recode it all, although at least this is not serialized anywhere
        public enum GameMenus
        {
            Crafting,
            Game,
            Factory,
            Equip,
            Storage,
            HeartboxUpgrade,
            HeartBoxRefuel,
            Scanner,
            Closed,
            Death,
            CircuitJuiceDeath,
            SceneStart,
            HeartBox,
            PlayerResearch,
            FastTravel,
            MiniMap
        }
        #endregion

        // This is a "tentative" selection of game menus that would make sense in Core. We can migrate some of them to
        // and from AtrioGameMenusEnum if needed
        public static readonly GameMenusEnum CRAFTING = new GameMenusEnum((int)GameMenus.Crafting, nameof(CRAFTING));
        public static readonly GameMenusEnum GAME = new GameMenusEnum((int)GameMenus.Game, nameof(GAME));
        public static readonly GameMenusEnum FACTORY = new GameMenusEnum((int)GameMenus.Factory, nameof(FACTORY));
        public static readonly GameMenusEnum EQUIP = new GameMenusEnum((int)GameMenus.Equip, nameof(EQUIP));
        public static readonly GameMenusEnum STORAGE = new GameMenusEnum((int)GameMenus.Storage, nameof(STORAGE));
        public static readonly GameMenusEnum DEATH = new GameMenusEnum((int)GameMenus.Death, nameof(DEATH));
        public static readonly GameMenusEnum SCENE_START = new GameMenusEnum((int)GameMenus.SceneStart, nameof(SCENE_START));
        public static readonly GameMenusEnum PLAYER_RESEARCH = new GameMenusEnum((int)GameMenus.PlayerResearch, nameof(PLAYER_RESEARCH));

        // Newly introduced enum names have no pre-associated value so they can auto-generate
        public static readonly GameMenusEnum CHEATER = new GameMenusEnum(nameof(CHEATER));
        public static readonly GameMenusEnum BACKPACK = new GameMenusEnum(nameof(BACKPACK));

        public GameMenusEnum(string name) : base(name)
        {

        }

        public GameMenusEnum(int value, string name) : base(value, name)
        {

        }
    }
}