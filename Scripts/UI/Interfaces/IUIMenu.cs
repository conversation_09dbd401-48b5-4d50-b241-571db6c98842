// Copyright Isto Inc.

using Isto.Core.StateMachine;

namespace Isto.Core.UI
{
    /// <summary>
    /// This interface serves to represent any menu state machine (there should only be one at a time).
    /// We can use this to figure out who needs to receive any substate we'd want to add on top.
    /// For instance: used for some popups
    /// </summary>
    public interface IUIMenu
    {
        /// <summary>
        /// Opens the specified menu, closing any already open menu first if needed.
        /// </summary>
        /// <param name="menu">The identifier of the menu to open.</param>
        /// <param name="args">Any relevant context for the menu if needed.</param>
        void OpenMenu(MenusEnum menu, OpenMenuArgs args = null);

        /// <summary>
        /// Closes all submenus and the current menu, and returns to the default state.
        /// </summary>
        void CloseMenu();

        /// <summary>
        /// Gives the identifier of the active ui menu. (ignores any substate)
        /// </summary>
        /// <returns>The current UI's associated MenusEnum</returns>
        MenusEnum CurrentMenu { get; }

        // These are "implementation details" and in an ideal world should not be exposed here.
        // For now we're keeping it because some old classes depend on using them. Ideally we'll refactor them.
        // Mostly they just need a name that makes sense from the outside and doesn't imply a state machine.
        // Might call them submenus
        // Might use an interface for a menu instead of a MonoState reference
        // Will have to see if we really need GetCurrentState, might have to refactor submenus in general too.
        /// <summary> Deprecated </summary>
        void EnterSubState(MonoState subState);
        /// <summary> Deprecated </summary>
        void ExitSubState();
        /// <summary> Deprecated </summary>
        MonoState GetCurrentState();
    }
}