// Copyright Isto Inc.

namespace Isto.Core.UI
{
    public interface IUIDisplayMessage
    {
        /// <summary>
        /// Implementation should display a message on screen for the specified amount of time.
        /// </summary>
        /// <param name="message">Message text to be shown</param>
        /// <param name="time">Time message should be displayed</param>
        void DisplayMessage(string message, float time);
    }
}