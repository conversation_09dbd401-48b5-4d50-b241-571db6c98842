using Isto.Core.Data;
using Isto.Core.Game;
using Isto.GTW.Managers;
using System.Collections.Generic;
using System.IO;
using System.Xml.Serialization;
using Zenject;

namespace Isto.GTW.Data
{
    /// <summary>
    /// Serializes the dictionaries of LevelVariables to save into the save file.
    ///
    /// Converts the dictionary to a list (since you can't serialize a dictionary)
    /// </summary>
    public class TitleScreenLevelDataManager : DataManager, IDataLoadCompleteHandler
    {
        private const string FILE_NAME = "level";
        private const string FILE_PREFIX = "/" + FILE_NAME + "_";

        public override string FilePrefix => FILE_PREFIX;
        public override string BlobName => FILE_NAME;


        // INJECTION

        private LevelVariables _levelVariables;
        private IGameProgressProvider _gameProgress;

        [Inject]
        public void Inject(LevelVariables levelVariables, IGameProgressProvider gameProgress)
        {
            _levelVariables = levelVariables;
            _gameProgress = gameProgress;
        }


        // OTHER METHODS

        public override bool Validate(in TextReader reader, in object previousDataObject)
        {
            bool valid = false;

            LevelSaveData previousData = previousDataObject as LevelSaveData;
            LevelSaveData loadedData = LoadXMLFile<LevelSaveData>(reader);

            if (loadedData != null)
            {
                valid = previousData.ContentEquals(loadedData);
            }

            return valid;
        }

        public override bool Load(in TextReader reader)
        {
            bool success = false;
            LevelSaveData levelSaveData = LoadXMLFile<LevelSaveData>(reader);

            if (levelSaveData != null)
            {
                _gameProgress.LoadLevelTimes(levelSaveData.GetLevelTimes());

                _levelVariables.boolVariables = new Dictionary<string, bool>();
                foreach (LevelSaveData.EntryBool entry in levelSaveData.boolVariables)
                {
                    _levelVariables.boolVariables.Add(entry.Key, (bool)entry.Value);
                }

                _levelVariables.intVariables = new Dictionary<string, int>();
                foreach (LevelSaveData.EntryInt entry in levelSaveData.intVariables)
                {
                    _levelVariables.intVariables.Add(entry.Key, (int)entry.Value);
                }

                _levelVariables.floatVariables = new Dictionary<string, float>();
                foreach (LevelSaveData.EntryFloat entry in levelSaveData.floatVariables)
                {
                    _levelVariables.floatVariables.Add(entry.Key, (float)entry.Value);
                }

                _levelVariables.OutputVariables();

                success = true;
            }

            return success;
        }

        public override void Save(out object saveData)
        {
            // We do NOT want to save any data from the TitleScreen
            saveData = null;
        }

        public override object GetSampleSaveData()
        {
            LevelSaveData levelSaveData = new LevelSaveData();
            return levelSaveData;
        }

        public override System.Type[] GetSaveExtraTypes()
        {
            return null;
        }

        public override void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer,
            out object saveData)
        {
            serializer = null;
            saveData = null;
        }

        public void OnDataLoadComplete()
        {

        }
    }
}