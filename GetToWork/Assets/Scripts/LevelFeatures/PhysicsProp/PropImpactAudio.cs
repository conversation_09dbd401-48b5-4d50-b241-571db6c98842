using System;
using FMODUnity;
using UnityEngine;

namespace LevelFeatures.PhysicsProp
{
    public class PropImpactAudio : MonoBehaviour
    {
        [System.Serializable]
        private struct ImpactAudioRef
        {
            public float impulse;
            [EventRef] public string _sfxRef;
        }
        
        
        // UNITY HOOKUPS
        
        [SerializeField] private ImpactAudioRef[] _impactAudio = new ImpactAudioRef[1];
        
        
        // UNITY LIFECYCLE

        private void OnCollisionEnter(Collision other)
        {
            Impact(other.impulse.magnitude, other.contacts[0].point);
        }


        // OTHER METHODS
        
        private void Impact(float hitImpulse, Vector3 position)
        {
            for (int i = _impactAudio.Length - 1; i >= 0; i--)
            {
                if (hitImpulse > _impactAudio[i].impulse)
                {
                    RuntimeManager.PlayOneShot(_impactAudio[i]._sfxRef, position);
                    break;
                }
            }
        }
    }
}