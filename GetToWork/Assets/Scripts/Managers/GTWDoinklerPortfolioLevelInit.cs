using System;
using System.Collections;
using Isto.Core.Game;
using Isto.GTW;
using Isto.GTW.Configuration;
using Isto.GTW.LevelFeatures;
using Isto.GTW.Managers;
using UnityEngine;
using Zenject;

namespace Managers
{
    public class GTWDoinklerPortfolioLevelInit : MonoBehaviour
    {
        // INJECTION
        
        private GTWGameState _gameState;
        private DoinklerLevelVariables _doinklerLevelVariables;

        [Inject]
        public void Inject(GTWGameState gameState, DoinklerLevelVariables levelVariables)
        {
            _gameState = gameState;
            _doinklerLevelVariables = levelVariables;
        }
        
        
        // UNITY LIFECYCLE
        
        private IEnumerator Start()
        {
            while (GameState.WaitingForSaveFileLoad)
            {
                yield return null;
            }
            
            if (GameState.GameModeLoadedFromGameState)
            {
                if (_gameState.CurrentGameMode != null && _gameState.GameLevelDefinition != null)
                {
                    //Load checkpoint specified in GameMode
                    GTWGameLevelDefinition levelDefinition = _gameState.GameLevelDefinition;

                    int attempts = _doinklerLevelVariables.GetInt(levelDefinition.UniqueIDAttempts);
                    _doinklerLevelVariables.SetInt(levelDefinition.UniqueIDAttempts, attempts + 1);
                }
            }
        }
    }
}