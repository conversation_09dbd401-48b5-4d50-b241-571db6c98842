using UnityEditor;
using UnityEngine;

namespace Isto.GTW.Managers.Editor
{
    [CustomEditor(typeof(LevelVariables))]
    public class LevelVariablesEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            
            LevelVariables levelVariables = target as LevelVariables;
            
            if (GUILayout.Button("Output variables"))
            {
                levelVariables.OutputVariables();
            }
        }
    }
}