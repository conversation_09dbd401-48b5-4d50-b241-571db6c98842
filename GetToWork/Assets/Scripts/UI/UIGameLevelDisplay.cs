// Copyright Isto Inc.

using Isto.Core.Localization;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// This is a button that represents the level so that users can level select.
    /// </summary>
    public class UIGameLevelDisplay : MonoBehaviour
    {
        // UNITY HOOKUP

        [SerializeField] private CoreButton _levelButton = null;
        [SerializeField] protected TextMeshProUGUI levelNumberLabel;
        [SerializeField] protected TextMeshProUGUI levelNameLabel;
        [SerializeField] protected TextMeshProUGUI attemptsLabel;
        [SerializeField] protected TextMeshProUGUI totalTimeLabel;
        [SerializeField] protected TextMeshProUGUI bestTimeLabel;
        [SerializeField] protected GameObject completedCheckMark;
        [SerializeField] protected GameObject isSelectedImage;
        [SerializeField] private ColorData _greyedOutTextColor;

        
        // OTHER FIELDS

        private GTWGameLevelDefinition _gameLevel = null; //Set up by the parent menu
        private UIDoinklerPortfolio _parentMenu = null;

        
        // PROPERTIES

        public GTWGameLevelDefinition GameLevel => _gameLevel;


        // EVENTS


        // INJECTION

        protected ILocalizationProvider _localization;
        protected LocTerm.Factory _localizedStringFactory;

        [Inject]
        public void Inject(ILocalizationProvider localization, LocTerm.Factory localizedFactory)
        {
            _localization = localization;
            _localizedStringFactory = localizedFactory;
        }


        // EVENT HANDLING

        public void Button_OnClick()
        {
            _parentMenu.LevelDisplayButton_OnPressed(_gameLevel);
        }

        // Bad method name. Bad logic. Would need to fix it but it's GTW only, so holding for now.
        /// <summary>
        /// This is basically a message or event telling the level button that a new level has been selected and that
        /// it should now decide if it is active or not based on that info.
        /// </summary>
        public void NewButtonActive(GTWGameLevelDefinition gameLevel)
        {
            bool active = gameLevel == _gameLevel;

            if (isSelectedImage.activeSelf != active)
            {
                isSelectedImage.SetActive(active);
            }
        }


        // ACCESSORS

        public void Setup(GTWGameLevelDefinition _gameLvl, UIDoinklerPortfolio _parentMenu)
        {
            _gameLevel = _gameLvl;
            this._parentMenu = _parentMenu;


            //QUESTION - understand how this works? Also i hate the dropdowns with a fiery passion. Is there a way around this?
            LocExpression gameModeTitle = CreateLocExpressionForGameMode(_gameLevel.LevelName.mTerm, _gameLevel.fallbackLevelName);
            gameModeTitle.LocalizeInto(levelNameLabel);

            levelNumberLabel.text = _gameLevel.LevelNumber.ToString("D2");
            attemptsLabel.text = _gameLevel.Attempts > 0 ? _gameLevel.Attempts.ToString() : "-";
            attemptsLabel.color = _gameLevel.Attempts > 0 ? Color.black : new Color(0.75f, 0.75f, 0.75f);
            totalTimeLabel.text = GTWUtils.GetFormattedTimeOrDefaultString(_gameLevel.TotalTimeInLevel, Color.black);
            bestTimeLabel.text = GTWUtils.GetFormattedTimeOrDefaultString(_gameLevel.FastestCompletedTime, Color.black);


            completedCheckMark.SetActive(_gameLevel.IsCompleted);

            /*
            // Game Mode Description
            LocExpression gameModeDescription = CreateLocExpressionForGameMode(_gameMode.DescriptionLoc.mTerm, _gameMode.internalName);
            gameModeDescription.LocalizeInto(modeDescriptionLabel);

            LayoutRebuilder.ForceRebuildLayoutImmediate(statusLayoutGroup.transform as RectTransform);

            */

            // Set availability
            bool allowed = (_gameLevel != null && _gameLevel.IsUnlocked) || _parentMenu.AllLevelsUnlocked;
            if (_levelButton != null)
            {
                _levelButton.interactable = allowed;
            }
        }


        // OTHER METHODS

        private LocExpression CreateLocExpressionForGameMode(string localizedTerm, string fallbackTerm)
        {
            LocTerm.LocalizationType type = LocTerm.LocalizationType.Localized;
            string term = localizedTerm;

            if (string.IsNullOrEmpty(localizedTerm))
            {
                type = LocTerm.LocalizationType.NonLocalized;
                term = fallbackTerm;
            }

            LocTerm gameModeExpression = _localizedStringFactory.Create(type, term);
            return new SingleTermLocExpression(gameModeExpression);
        }
    }
}