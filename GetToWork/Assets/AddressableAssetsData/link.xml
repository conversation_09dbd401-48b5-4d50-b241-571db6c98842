<linker>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BakeryVolumeApplicator" preserve="all" />
    <type fullname="BakeryVolumeRegistry" preserve="all" />
    <type fullname="CashPickup" preserve="all" />
    <type fullname="Clock" preserve="all" />
    <type fullname="Code.Scripts.LevelFeatures.AnimatedPhysicsObject.RotatingObject" preserve="all" />
    <type fullname="Code.Scripts.LevelFeatures.AnimatedPhysicsObject.RotatingObjectByStep" preserve="all" />
    <type fullname="Code.Scripts.LevelFeatures.Bumper.Bumper" preserve="all" />
    <type fullname="Code.Scripts.LevelFeatures.LaunchPad.JumpPad" preserve="all" />
    <type fullname="DynamicFogAndMist2.DynamicFog" preserve="all" />
    <type fullname="DynamicFogAndMist2.DynamicFogManager" preserve="all" />
    <type fullname="DynamicFogAndMist2.DynamicFogProfile" preserve="all" />
    <type fullname="Isto.GTW.Audio.EndGameMusic" preserve="all" />
    <type fullname="Isto.GTW.Audio.ObstacleAudioLooping" preserve="all" />
    <type fullname="Isto.GTW.Audio.ObstacleAudioOneShot" preserve="all" />
    <type fullname="Isto.GTW.Audio.VolumetricAmbience" preserve="all" />
    <type fullname="Isto.GTW.Camera.FixedVirtualCamera" preserve="all" />
    <type fullname="Isto.GTW.Configuration.GTWGameLevelDefinition" preserve="all" />
    <type fullname="Isto.GTW.LevelEvents.LevelEvents.DialogueEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelEvents.LevelEvents.PickupAudioEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.Checkpoint" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.AchievementEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.ActivateCheckpointEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.ActivateGameObjectEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.AnimatorSetBoolEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.ChangeGameStateEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.ChoicePopupEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.DelayTriggerEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.DialogueEventTriggerSerializer" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.DoinklerPortfolioEndPopupEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.DoinklerStageEventTriggerSerializer" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.DunceHatPickupEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.HidePlatformEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.IgnoreLevelDataCollidersEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.InternalNamedEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.IntSwitchTriggerEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.LevelEventTriggerSerializer" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.LevelFallDialogueEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.LevelTransitionPopupEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.PlayerCollisionEventTriggerCompound" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.PlayMovingObjectEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.ResetHidePlatformEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetIntLevelVariableEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetPlayerAllowGrabAbilityEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetPlayerAllowRagdollAbilityEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetPlayerAllowRagdollRecoveryEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetPlayerOutlineEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetPlayerRagdollEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetPlayerRagdollResetOnInputEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetSkyboxEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.SetVirtualCameraEvent" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.DialogueCompletedEventTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.PlayerCameraOverrideTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.PlayerCollisionEventTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.PlayerCollisionFailureEventTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.PlayerEventTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.PlayerGrabEventTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEventTriggers.PlayerPhysicsOverrideTrigger" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.LevelFallDialogueSystem" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.MovingObject" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.MovingObjectUiIndicator" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.Wearables.CharacterWearableData" preserve="all" />
    <type fullname="Isto.GTW.Localization.CanvasTooltipLocalization" preserve="all" />
    <type fullname="Isto.GTW.Player.RagdollCollisionHandler" preserve="all" />
    <type fullname="Isto.GTW.Player.States.GTWPlayerCutsceneState" preserve="all" />
    <type fullname="Isto.GTW.SectionLoading.LevelSection" preserve="all" />
    <type fullname="Isto.GTW.Tools.Anim.SetAnimParam.SetAnimatorFloat" preserve="all" />
    <type fullname="Isto.GTW.Tools.Anim.StateBehavior.DisableAnimatorStateBehaviorEvent" preserve="all" />
    <type fullname="Isto.GTW.Tools.Anim.StateBehavior.SetBoolStateBehaviorEvent" preserve="all" />
    <type fullname="Isto.GTW.UI.UIDoinklerLeaderboardCheckpoint" preserve="all" />
    <type fullname="Isto.GTW.WorldTextToSubtitle" preserve="all" />
    <type fullname="LevelFeatures.PhysicsProp.PropImpactAudio" preserve="all" />
    <type fullname="Outline" preserve="all" />
    <type fullname="UI.UITools.UsingControllerGameObjectActivation" preserve="all" />
    <type fullname="UnityStandardAssets.Water.WaterBasic" preserve="all" />
    <type fullname="Isto.GTW.LevelFeatures.MovingObject/MoveStep" preserve="nothing" serialized="true" />
    <type fullname="Isto.GTW.Camera.PlayerCameraOverride/CameraOverride" preserve="nothing" serialized="true" />
    <type fullname="Isto.GTW.LevelFeatures.LevelEvents.IntSwitchTriggerEvent/EventSwitchCase" preserve="nothing" serialized="true" />
    <type fullname="LevelFeatures.PhysicsProp.PropImpactAudio/ImpactAudioRef" preserve="nothing" serialized="true" />
    <type fullname="Isto.GTW.Player.PlayerPhysicsOverride/PhysicsOverride" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="BakeryRuntimeAssembly, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BakeryDirectLight" preserve="all" />
    <type fullname="BakeryLightMesh" preserve="all" />
    <type fullname="BakeryPointLight" preserve="all" />
    <type fullname="BakerySector" preserve="all" />
    <type fullname="BakeryVolume" preserve="all" />
    <type fullname="ftLightmapsStorage" preserve="all" />
    <type fullname="ftGlobalStorage/AdjustedMesh" preserve="nothing" serialized="true" />
    <type fullname="ftGlobalStorage/AtlasPacker" preserve="nothing" serialized="true" />
    <type fullname="ftLightmapsStorage/LightData" preserve="nothing" serialized="true" />
    <type fullname="ftLightmapsStorage/SectorData" preserve="nothing" serialized="true" />
    <type fullname="ftLightmapsStorage/L2" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Cinemachine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Cinemachine.CinemachineComposer" preserve="all" />
    <type fullname="Cinemachine.CinemachinePipeline" preserve="all" />
    <type fullname="Cinemachine.CinemachineTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineVirtualCamera" preserve="all" />
    <type fullname="Cinemachine.CinemachineBrain/VcamActivatedEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineVirtualCameraBase/TransitionParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.LensSettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Decalery, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="DecalGroup" preserve="all" />
  </assembly>
  <assembly fullname="FMODUnity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="FMODUnity.StudioEventEmitter" preserve="all" />
    <type fullname="FMOD.GUID" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.EventReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Isto.Core, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Isto.Core.Configuration.GameModeDefinition" preserve="all" />
    <type fullname="Isto.Core.Configuration.PlatformDependent" preserve="all" />
    <type fullname="Isto.Core.Inputs.ControlSettingsChangedListener" preserve="all" />
  </assembly>
  <assembly fullname="Plugins, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="I2.Loc.Localize" preserve="all" />
    <type fullname="I2.Loc.LocalizeTarget_TextMeshPro_UGUI" preserve="all" />
    <type fullname="I2.Loc.LocalizeTarget_UnityStandard_Prefab" preserve="all" />
    <type fullname="PixelCrushers.DialogueSystem.Wrappers.DialogueDatabase" preserve="all" />
    <type fullname="PixelCrushers.DialogueSystem.Wrappers.DialogueSystemTrigger" preserve="all" />
    <type fullname="I2.Loc.LocalizedString" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.Actor" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.Conversation" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.ConversationOverrideDisplaySettings" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.DialogueDatabase/SyncInfo" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.DialogueEntry" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.EmphasisSetting" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.Field" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.Link" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.Variable" preserve="nothing" serialized="true" />
    <type fullname="I2.Loc.EventCallback" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.DialogueSystem.Condition" preserve="nothing" serialized="true" />
    <type fullname="PixelCrushers.GameObjectUnityEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalLightData" preserve="all" />
    <type fullname="VolumetricLights.VolumetricLight" preserve="all" />
    <type fullname="VolumetricLights.VolumetricLightProfile" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.TemporalAA/Settings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.TMP_SpriteAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.GlyphAnchorPoint" preserve="nothing" serialized="true" />
    <type fullname="TMPro.MarkPositionAdjustment" preserve="nothing" serialized="true" />
    <type fullname="TMPro.MarkToBaseAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.LigatureSubstitutionRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.MarkToMarkAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Sprite" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.ComputeShader" preserve="all" />
    <type fullname="UnityEngine.Cubemap" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightingSettings" preserve="all" />
    <type fullname="UnityEngine.LightmapSettings" preserve="all" />
    <type fullname="UnityEngine.LightProbes" preserve="all" />
    <type fullname="UnityEngine.LODGroup" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.ReflectionProbe" preserve="all" />
    <type fullname="UnityEngine.RenderSettings" preserve="all" />
    <type fullname="UnityEngine.RenderTexture" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Texture3D" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.CharacterJoint" preserve="all" />
    <type fullname="UnityEngine.FixedJoint" preserve="all" />
    <type fullname="UnityEngine.HingeJoint" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.PhysicsMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TerrainModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Terrain" preserve="all" />
    <type fullname="UnityEngine.TerrainData" preserve="all" />
    <type fullname="UnityEngine.TerrainLayer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TerrainPhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.TerrainCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.EventSystems.EventSystem" preserve="all" />
    <type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.VFXModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.VFX.VFXRenderer" preserve="all" />
    <type fullname="UnityEngine.VFX.VisualEffect" preserve="all" />
    <type fullname="UnityEngine.VFX.VisualEffectAsset" preserve="all" />
  </assembly>
  <assembly fullname="Zenject, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Zenject.SceneContext" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
</linker>